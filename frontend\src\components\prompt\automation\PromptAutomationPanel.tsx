// src/components/prompt/automation/PromptAutomationPanel.tsx
import React from 'react';
import { PromptPlaybackControls } from './PromptPlaybackControls';
import '../../resolution/automation/AutomationPanel.css';

/**
 * Composant principal pour la section Automatisation de l'onglet Prompt (IA)
 * Note: L'éditeur de commandes a été supprimé - utiliser celui de l'onglet Résolution
 */
export const PromptAutomationPanel: React.FC = () => {
  return (
    <div className="automation-panel">
      <div className="automation-header">
        <h2>Automatisation :</h2>
        <p style={{ fontSize: '0.9rem', color: 'var(--text-secondary)', margin: '0.5rem 0 0 0' }}>
          Utilisez l'éditeur de commandes dans l'onglet Résolution
        </p>
      </div>
      <div className="automation-content">
        <PromptPlaybackControls />
      </div>
    </div>
  );
};
