#!/usr/bin/env python3
"""
Test des nouvelles commandes MOTIF, MULTIPLIER et DIVISER
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from command_system.command_executor import CommandExecutor
import numpy as np

def test_motif():
    """Test de la commande MOTIF"""
    print("🧪 Test MOTIF...")
    
    executor = CommandExecutor()
    
    # Initialiser une grille 6x6
    commands = [
        "INIT 6x6",
        "EDIT 1 [0,0]",
        "EDIT 4 [0,1]", 
        "EDIT 1 [0,2]",
        "EDIT 4 [1,0]",
        "EDIT 9 [1,1]",
        "EDIT 4 [1,2]",
        "EDIT 9 [2,0]",
        "EDIT 1 [2,1]",
        "EDIT 9 [2,2]",
        "MOTIF {COPY [0,0 2,2]; ROTATE RIGHT [0,3 2,5]; PASTE [0,3]}"
    ]
    
    for cmd in commands:
        result = executor._execute_command(cmd)
        if not result:
            print(f"❌ Échec commande: {cmd}")
            print(f"   Erreur: {executor.error}")
            return False
        print(f"✅ {cmd}")
    
    print("✅ Test MOTIF réussi!")
    return True

def test_multiplier():
    """Test de la commande MULTIPLIER"""
    print("🧪 Test MULTIPLIER...")
    
    executor = CommandExecutor()
    
    commands = [
        "INIT 6x6",
        "EDIT 1 [0,0]",
        "EDIT 0 [0,1]",
        "EDIT 0 [1,0]", 
        "EDIT 1 [1,1]",
        "MULTIPLIER 2 [0,0 1,1]"
    ]
    
    for cmd in commands:
        result = executor._execute_command(cmd)
        if not result:
            print(f"❌ Échec commande: {cmd}")
            print(f"   Erreur: {executor.error}")
            return False
        print(f"✅ {cmd}")
    
    # Vérifier le résultat
    expected = np.array([
        [1, 1, 0, 0, 0, 0],
        [1, 1, 0, 0, 0, 0],
        [0, 0, 1, 1, 0, 0],
        [0, 0, 1, 1, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0]
    ])
    
    if np.array_equal(executor.grid, expected):
        print("✅ Test MULTIPLIER réussi!")
        return True
    else:
        print("❌ Test MULTIPLIER échoué - résultat inattendu")
        print("Attendu:")
        print(expected)
        print("Obtenu:")
        print(executor.grid)
        return False

def test_diviser():
    """Test de la commande DIVISER"""
    print("🧪 Test DIVISER...")
    
    executor = CommandExecutor()
    
    commands = [
        "INIT 6x6",
        "EDIT 1 [0,0]",
        "EDIT 1 [0,1]",
        "EDIT 0 [0,2]",
        "EDIT 0 [0,3]",
        "EDIT 1 [1,0]",
        "EDIT 1 [1,1]", 
        "EDIT 0 [1,2]",
        "EDIT 0 [1,3]",
        "EDIT 0 [2,0]",
        "EDIT 0 [2,1]",
        "EDIT 1 [2,2]",
        "EDIT 1 [2,3]",
        "EDIT 0 [3,0]",
        "EDIT 0 [3,1]",
        "EDIT 1 [3,2]",
        "EDIT 1 [3,3]",
        "DIVISER 2 [0,0 3,3]"
    ]
    
    for cmd in commands:
        result = executor._execute_command(cmd)
        if not result:
            print(f"❌ Échec commande: {cmd}")
            print(f"   Erreur: {executor.error}")
            return False
        print(f"✅ {cmd}")
    
    # Vérifier le résultat
    expected = np.array([
        [1, 0, 0, 0, 0, 0],
        [0, 1, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0]
    ])
    
    if np.array_equal(executor.grid, expected):
        print("✅ Test DIVISER réussi!")
        return True
    else:
        print("❌ Test DIVISER échoué - résultat inattendu")
        print("Attendu:")
        print(expected)
        print("Obtenu:")
        print(executor.grid)
        return False

def main():
    """Fonction principale"""
    print("🚀 Test des nouvelles commandes...")
    
    tests = [test_motif, test_multiplier, test_diviser]
    passed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Exception dans {test.__name__}: {e}")
    
    print(f"\n📊 Résultats: {passed}/{len(tests)} tests réussis")
    
    if passed == len(tests):
        print("🎉 Tous les tests sont passés!")
        return 0
    else:
        print("🚨 Certains tests ont échoué")
        return 1

if __name__ == "__main__":
    sys.exit(main())
