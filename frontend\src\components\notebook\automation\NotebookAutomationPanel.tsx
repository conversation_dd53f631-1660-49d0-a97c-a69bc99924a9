// src/components/notebook/automation/NotebookAutomationPanel.tsx
import React from 'react';
import { NotebookPlaybackControls } from './NotebookPlaybackControls';
import styles from './NotebookAutomation.module.css';

/**
 * Composant principal pour la section Automatisation de l'onglet Notebook
 * Note: L'éditeur de commandes a été supprimé - utiliser celui de l'onglet Résolution
 */
export const NotebookAutomationPanel: React.FC = () => {
  return (
    <div className={styles.automationPanel}>
      <div className={styles.automationHeader}>
        <h2>Automatisation :</h2>
        <p style={{ fontSize: '0.9rem', color: 'var(--text-secondary)', margin: '0.5rem 0 0 0' }}>
          Utilisez l'éditeur de commandes dans l'onglet Résolution
        </p>
      </div>
      <div className={styles.automationContent}>
        <NotebookPlaybackControls />
      </div>
    </div>
  );
};
